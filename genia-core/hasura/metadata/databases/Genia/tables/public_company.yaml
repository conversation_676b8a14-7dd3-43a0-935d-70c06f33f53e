table:
  name: company
  schema: public
configuration:
  column_config:
    created_at:
      custom_name: createdAt
    id:
      custom_name: id
    name:
      custom_name: name
    reference_id:
      custom_name: referenceId
    updated_at:
      custom_name: updatedAt
  custom_column_names:
    created_at: createdAt
    id: id
    name: name
    reference_id: referenceId
    updated_at: updatedAt
  custom_root_fields: {}
array_relationships:
  - name: attributes
    using:
      foreign_key_constraint_on:
        column: company_id
        table:
          name: attribute
          schema: public
  - name: catalog_discounts
    using:
      foreign_key_constraint_on:
        column: company_id
        table:
          name: catalog_discount
          schema: public
  - name: catalogs
    using:
      foreign_key_constraint_on:
        column: company_id
        table:
          name: catalog
          schema: public
  - name: clients
    using:
      foreign_key_constraint_on:
        column: company_id
        table:
          name: client
          schema: public
  - name: clientsByClientCompanyId
    using:
      foreign_key_constraint_on:
        column: client_company_id
        table:
          name: client
          schema: public
  - name: inventories
    using:
      foreign_key_constraint_on:
        column: company_id
        table:
          name: inventory
          schema: public
  - name: provider_of
    using:
      foreign_key_constraint_on:
        column: provider_company_id
        table:
          name: provider
          schema: public
  - name: store_discounts
    using:
      foreign_key_constraint_on:
        column: company_id
        table:
          name: store_discount
          schema: public
  - name: user_companies
    using:
      foreign_key_constraint_on:
        column: company_id
        table:
          name: user_company
          schema: public
select_permissions:
  - role: read:hasura
    permission:
      columns:
        - id
        - name
        - reference_id
      filter: {}
    comment: ""
  - role: read_user
    permission:
      columns:
        - id
        - name
        - created_at
        - updated_at
      filter:
        _or:
          - id:
              _eq: X-Hasura-Company-Id
          - clients:
              client_company_id:
                _eq: X-Hasura-Company-Id
      allow_aggregations: true
    comment: ""
