table:
  name: user
  schema: public
configuration:
  column_config:
    app_phone_number:
      custom_name: app<PERSON><PERSON><PERSON><PERSON><PERSON>
    created_at:
      custom_name: createdAt
    email:
      custom_name: email
    id:
      custom_name: id
    updated_at:
      custom_name: updatedAt
  custom_column_names:
    app_phone_number: app<PERSON><PERSON><PERSON><PERSON>ber
    created_at: createdAt
    email: email
    id: id
    updated_at: updatedAt
  custom_root_fields: {}
array_relationships:
  - name: inventoryHistories
    using:
      foreign_key_constraint_on:
        column: user_id
        table:
          name: inventory_history
          schema: public
  - name: userCompanies
    using:
      foreign_key_constraint_on:
        column: user_id
        table:
          name: user_company
          schema: public
  - name: userReferences
    using:
      foreign_key_constraint_on:
        column: user_id
        table:
          name: user_reference
          schema: public
select_permissions:
  - role: read:hasura
    permission:
      columns:
        - app_phone_number
        - created_at
        - email
        - id
        - updated_at
      filter: {}
    comment: ""
  - role: read_user
    permission:
      columns:
        - created_at
        - email
        - id
        - updated_at
        - app_phone_number
      filter:
        userCompanies:
          company_id:
            _eq: X-Hasura-Company-Id
      allow_aggregations: true
    comment: ""
