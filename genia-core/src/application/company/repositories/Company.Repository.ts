import CompanyEntity from '#domain/aggregates/company/Company.Entity';

interface CompanyRepository {
  save(company: CompanyEntity, referenceId: string): Promise<CompanyEntity>;
  findOneByReferenceId(referenceId: string): Promise<CompanyEntity | undefined>;
  findManyByIds(ids: string[]): Promise<CompanyEntity[]>;
  findOneById(id: string): Promise<CompanyEntity | undefined>;
}

export default CompanyRepository;
